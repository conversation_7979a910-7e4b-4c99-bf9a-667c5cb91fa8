#include "User_Control.h"
#include "User_IR_Sensor.h"
#include "User_Parameter.h"
#include "key.h"
#include "counting.h"
#include "ti_msp_dl_config.h"
#include "oled.h"

volatile int flag = 0;
volatile int i = 0;//拐角数
volatile int j = 0;//圈数

// 添加延时计数器变量
static volatile unsigned int delay_counter = 0;  // 防重复检测延时计数器
static const unsigned int DELAY_MS = 100;       // 50ms防重复延时（缩短）

// 添加圈数计时器变量
static volatile unsigned int lap_timer = 0;      // 圈数计时器
static const unsigned int LAP_TIME_S = 14000;    // 15秒对应15000个1ms中断
static volatile unsigned int total_time_limit = 0; // 总时间限制（动态计算）

// 添加按键处理的非阻塞变量
static volatile unsigned int key_delay_counter = 0;  // 按键消抖计数器
static volatile unsigned char key_pressed = 0;       // 按键按下标志
static const unsigned int KEY_DEBOUNCE_MS = 20;      // 20ms消抖时间

// 目标圈数变量
int Target_Laps = 1;  // 默认设置为2圈

void counting(void)
{
	// 计算总时间限制（圈数 * 每圈时间）
	total_time_limit = Target_Laps * LAP_TIME_S;

	// 防重复检测延时计数器递减
	if(delay_counter > 0)
	{
		delay_counter--;
	}

	// 总计时器递增（倒计时逻辑）
	if(lap_timer < total_time_limit)
	{
		lap_timer++;
	}

	// 拐角检测逻辑 - 防重复延时期间不计数
	if((Error >= 13 && Error <= 15) && delay_counter == 0)
	{
		i++;  // 拐角计数
		delay_counter = DELAY_MS;  // 设置100ms防重复延时
	}

	// 圈数计算逻辑 - 双重条件：4个拐角 + 对应的时间
	if(i >= 4 && lap_timer >= (j + 1) * LAP_TIME_S)
	{
		i = 0;           // 重置拐角计数
		j++;             // 圈数+1
	}
	else if(lap_timer >= (j + 1) * LAP_TIME_S && i < 4)
	{
		// 如果时间到了但拐角不足4个，直接停车
		Set_Motor(0,0);
		return;  // 直接返回，停止后续处理
	}

	// 停车逻辑 - 达到目标圈数且时间条件满足才停车
	if(j >= Target_Laps && lap_timer >= total_time_limit)
	{
		Set_Motor(0,0);
	}
}



void keycontrol(void)
{
	// 按键消抖计数器递减
	if(key_delay_counter > 0)
	{
		key_delay_counter--;
		return;  // 在消抖期间不处理按键
	}

	// 检测按键按下
	if (KEYL == 0 && key_pressed == 0)  // 按键按下且未处理过
	{
		key_pressed = 1;  // 标记按键已按下
		key_delay_counter = KEY_DEBOUNCE_MS;  // 设置消抖延时

		// 处理按键逻辑
		Target_Laps++;  // 每次按下增加圈数
		if (Target_Laps > 5) Target_Laps = 1;  // 循环设定1-5圈
		OLEDLCD_Put6x7Num(90,9,Target_Laps,3,1);  // 显示目标圈数
	}
	else if (KEYL != 0)  // 按键释放
	{
		key_pressed = 0;  // 重置按键标志
	}
}
	























































