#include "User_Control.h"
#include "User_IR_Sensor.h"
#include "User_Parameter.h"
#include "key.h"
#include "counting.h"
#include "ti_msp_dl_config.h"
#include "oled.h"

volatile int flag = 0;
volatile int i = 0;//拐角数
volatile int j = 0;//圈数

// 添加延时计数器变量
static volatile unsigned int delay_counter = 0;  // 防重复检测延时计数器
static const unsigned int DELAY_MS = 100;       // 50ms防重复延时（缩短）

// 添加圈数计时器变量
static volatile unsigned int lap_timer = 0;      // 圈数计时器
static const unsigned int LAP_TIME_S = 15000;    // 15秒对应15000个1ms中断
static volatile unsigned int total_time_limit = 0; // 总时间限制（动态计算）

// 添加按键处理的非阻塞变量
static volatile unsigned int key_delay_counter = 0;  // 按键消抖计数器
static volatile unsigned char key_pressed = 0;       // 按键按下标志
static const unsigned int KEY_DEBOUNCE_MS = 20;      // 20ms消抖时间

// 目标圈数变量
int Target_Laps = 1;  // 默认设置为2圈

void counting(void)
{
	// 防重复检测延时计数器递减
	if(delay_counter > 0)
	{
		delay_counter--;
	}

	// 圈数计时器递增（始终计时）
	if(lap_timer < LAP_TIME_S)
	{
		lap_timer++;
	}

	// 拐角检测逻辑 - 防重复延时期间不计数
	if((Error >= 13 && Error <= 15) && delay_counter == 0)
	{
		i++;  // 拐角计数
		delay_counter = DELAY_MS;  // 设置50ms防重复延时（缩短）
	}

	// 圈数计算逻辑 - 双重条件：4个拐角 + 10秒时间
	if(i >= 4 && lap_timer >= LAP_TIME_S)
	{
		i = 0;           // 重置拐角计数
		j++;             // 圈数+1
		lap_timer = 0;   // 重置计时器，开始下一圈计时
	}
	else if(lap_timer >= LAP_TIME_S && i < 4)
	{
		// 如果10秒到了但拐角不足4个，重置计时器继续等待
		lap_timer = 0;
		// i保持不变，继续累积拐角数
	}

	// 停车逻辑 - 达到目标圈数就停车
	if(j >= Target_Laps && Target_Laps > 0)
	{
		Set_Motor(0,0);
	}
}



void keycontrol(void)
{
	// 按键消抖计数器递减
	if(key_delay_counter > 0)
	{
		key_delay_counter--;
		return;  // 在消抖期间不处理按键
	}

	// 检测按键按下
	if (KEYL == 0 && key_pressed == 0)  // 按键按下且未处理过
	{
		key_pressed = 1;  // 标记按键已按下
		key_delay_counter = KEY_DEBOUNCE_MS;  // 设置消抖延时

		// 处理按键逻辑
		Target_Laps++;  // 每次按下增加圈数
		if (Target_Laps > 5) Target_Laps = 1;  // 循环设定1-5圈
		OLEDLCD_Put6x7Num(90,9,Target_Laps,3,1);  // 显示目标圈数
	}
	else if (KEYL != 0)  // 按键释放
	{
		key_pressed = 0;  // 重置按键标志
	}
}
	























































